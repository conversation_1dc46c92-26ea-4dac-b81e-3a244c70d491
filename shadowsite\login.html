<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>تسجيل الدخول - CyberSultan</title>
  <style>
    body {
      background-color: #0d0d0d;
      color: #00ff99;
      font-family: 'Courier New', monospace;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      margin: 0;
    }
    .login-container {
      background: #121212;
      padding: 40px 30px;
      border-radius: 15px;
      box-shadow: 0 0 10px #00ff99;
      width: 320px;
      text-align: center;
      animation: fadeIn 1.5s ease;
    }
    h2 {
      margin-bottom: 25px;
    }
    input[type="text"], input[type="password"], input[type="email"], input[type="tel"], input[type="date"] {
      width: 100%;
      padding: 12px 10px;
      margin: 12px 0;
      border: none;
      border-radius: 8px;
      font-size: 1em;
      background: #222;
      color: #00ff99;
      box-sizing: border-box;
    }
    textarea {
      width: 100%;
      padding: 12px 10px;
      margin: 12px 0;
      border: none;
      border-radius: 8px;
      font-size: 1em;
      background: #222;
      color: #00ff99;
      box-sizing: border-box;
      height: 80px;
      resize: none;
    }
    input::placeholder, textarea::placeholder {
      color: #00cc7a;
    }
    button {
      width: 100%;
      padding: 12px 0;
      background: #00ff99;
      border: none;
      border-radius: 8px;
      font-size: 1.1em;
      color: #000;
      cursor: pointer;
      transition: background 0.3s ease;
      margin-top: 15px;
    }
    button:hover {
      background: #00cc7a;
    }
    .message {
      margin-top: 20px;
      font-size: 0.9em;
      color: #00ffe7;
    }
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    .social-icons {
      margin-top: 15px;
    }
    .social-icons a {
      color: #00ff99;
      font-size: 1.5em;
      margin: 0 10px;
      text-decoration: none;
    }
  </style>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" />
</head>
<body>

  <div class="login-container">
    <h2>تسجيل الدخول</h2>
    <form id="loginForm">
      <input type="text" id="userInput" placeholder="اسم المستخدم" required />
      <input type="email" id="emailInput" placeholder="البريد الإلكتروني" required />
      <input type="tel" id="phoneInput" placeholder="رقم الهاتف" required />
      <input type="password" id="passwordInput" placeholder="كلمة المرور" required />
      <input type="date" id="birthDateInput" placeholder="تاريخ الميلاد" required />
      <textarea id="ambitionInput" placeholder="طموحك المستقبلي"></textarea>
      <button type="submit">دخول</button>
    </form>
    <div class="social-icons">
      <a href="#" title="Facebook"><i class="fab fa-facebook"></i></a>
      <a href="#" title="Twitter"><i class="fab fa-twitter"></i></a>
      <a href="#" title="GitHub"><i class="fab fa-github"></i></a>
    </div>
    <div class="message" id="message"></div>
  </div>

  <script>
    const form = document.getElementById('loginForm');
    const message = document.getElementById('message');

    form.addEventListener('submit', function(e) {
      e.preventDefault();

      const user = document.getElementById('userInput').value.trim();
      const email = document.getElementById('emailInput').value.trim();
      const phone = document.getElementById('phoneInput').value.trim();
      const pass = document.getElementById('passwordInput').value.trim();
      const birthDate = document.getElementById('birthDateInput').value.trim();
      const ambition = document.getElementById('ambitionInput').value.trim();

      if(user && email && phone && pass && birthDate) {
        // تخزين بيانات المستخدم
        localStorage.setItem('username', user);
        localStorage.setItem('email', email);
        localStorage.setItem('phone', phone);
        localStorage.setItem('birthDate', birthDate);
        localStorage.setItem('ambition', ambition);
        localStorage.setItem('loggedIn', 'true');
        
        // عرض رسالة نجاح
        message.style.color = '#00ff99';
        message.textContent = `مرحبًا بك يا ${user} في عالم CyberSultan!`;
        
        // الانتقال إلى صفحة الملف الشخصي بعد 3 ثوان
        setTimeout(() => {
          window.location.href = 'profile.html';
        }, 3000);
      } else {
        message.style.color = '#ff0033';
        message.textContent = 'يرجى تعبئة جميع الحقول المطلوبة';
      }
    });
  </script>

</body>
</html>
