// تغيير مسار ملف الأدوات ليكون صحيحًا
fetch('./unique_tools.json')
  .then(response => response.json())
  .then(data => {
    const container = document.getElementById('tools-container');
    const searchBox = document.getElementById('searchBox');

    // إزالة التكرارات من الأدوات
    const uniqueTools = [];
    const toolNames = new Set();
    
    data.forEach(tool => {
      if (!toolNames.has(tool.name)) {
        toolNames.add(tool.name);
        uniqueTools.push(tool);
      }
    });

    function displayTools(tools) {
      container.innerHTML = "";
      tools.forEach(tool => {
        const div = document.createElement('div');
        div.className = 'tool-card';
        
        // إضافة صورة افتراضية بدون الاعتماد على وجود صور خارجية
        div.innerHTML = `
          <div class="tool-icon">${tool.name.charAt(0)}</div>
          <h2>${tool.name}</h2>
          <p>${tool.description}</p>
          <code>${tool.command}</code><br><br>
          <a href="${tool.download}" target="_blank">📦 تحميل الأداة</a>
        `;
        container.appendChild(div);
      });
    }

    displayTools(uniqueTools);

    searchBox.addEventListener('input', () => {
      const keyword = searchBox.value.toLowerCase();
      const filtered = uniqueTools.filter(tool =>
        tool.name.toLowerCase().includes(keyword) ||
        tool.description.toLowerCase().includes(keyword)
      );
      displayTools(filtered);
    });
  })
  .catch(error => {
    console.error('خطأ في تحميل ملف الأدوات:', error);
    document.getElementById('tools-container').innerHTML = 
      '<div class="error-message">حدث خطأ في تحميل الأدوات. يرجى التحقق من وجود ملف unique_tools.json</div>';
  });

// إضافة وظيفة تسجيل الدخول المحدثة
document.addEventListener('DOMContentLoaded', function() {
  const loginBtn = document.getElementById('loginBtn');
  const loginModal = document.getElementById('loginModal');
  const closeBtn = document.querySelector('.close');
  const loginForm = document.getElementById('loginForm');
  const welcomeMessage = document.getElementById('welcomeMessage');

// سيتم تحديث زر تسجيل الدخول بعد إرسال النموذج

  // فتح نافذة تسجيل الدخول
  if (loginBtn) {
    loginBtn.addEventListener('click', function(e) {
      e.preventDefault();
      loginModal.style.display = 'block';
    });
  }

  // إغلاق النافذة
  if (closeBtn) {
    closeBtn.addEventListener('click', function() {
      loginModal.style.display = 'none';
    });
  }

  // إغلاق النافذة عند النقر خارجها
  window.addEventListener('click', function(e) {
    if (e.target === loginModal) {
      loginModal.style.display = 'none';
    }
  });

  // إضافة مستمع لمفتاح Enter في النموذج
  if (loginForm) {
    loginForm.addEventListener('keydown', function(e) {
      if (e.key === 'Enter') {
        // إظهار تأثير بصري لأيقونة Enter
        const submitBtn = document.querySelector('.submit-btn');
        if (submitBtn) {
          submitBtn.innerHTML = '⏎ جاري التسجيل...';
          submitBtn.style.backgroundColor = '#00cc7a';
        }
      }
    });
  }

  // معالجة إرسال النموذج
  if (loginForm) {
    loginForm.addEventListener('submit', function(e) {
      e.preventDefault();

      // التحقق من صحة النموذج
      if (!validateForm()) {
        return;
      }

      // جمع بيانات النموذج
      const formData = new FormData(loginForm);
      const userData = {
        fullName: formData.get('fullName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        password: formData.get('password'),
        confirmPassword: formData.get('confirmPassword'),
        github: formData.get('github'),
        birthdate: formData.get('birthdate'),
        country: formData.get('country'),
        experience: formData.get('experience'),
        ambitions: formData.get('ambitions'),
        comments: formData.get('comments'),
        terms: formData.get('terms'),
        newsletter: formData.get('newsletter'),
        registrationDate: new Date().toISOString()
      };

      // حفظ البيانات في التخزين المحلي
      localStorage.setItem('userData', JSON.stringify(userData));
      localStorage.setItem('loggedIn', 'true');

      // إظهار رسالة الترحيب
      if (welcomeMessage) {
        welcomeMessage.style.display = 'block';
        setTimeout(() => {
          welcomeMessage.style.display = 'none';
        }, 5000);
      }

      // إغلاق النافذة
      loginModal.style.display = 'none';

      // إعادة تعيين زر الإرسال
      const submitBtn = document.querySelector('.submit-btn');
      if (submitBtn) {
        submitBtn.innerHTML = 'تسجيل الدخول';
        submitBtn.style.backgroundColor = '#00ff99';
      }

      // تحديث زر تسجيل الدخول مع أيقونة Enter
      loginBtn.innerHTML = `<span style="margin-right: 8px;">⏎</span>مرحباً ${userData.fullName}`;
      loginBtn.style.backgroundColor = '#00cc7a';
      loginBtn.style.display = 'flex';
      loginBtn.style.alignItems = 'center';
      loginBtn.style.justifyContent = 'center';

      // إظهار رسالة نجاح مع أيقونة
      alert('⏎ تم تسجيل الدخول بنجاح! مرحباً بك في ShadowSec Tools');

      // تحديث عداد الزوار
      updateVisitorCount();
    });
  }

  // التحقق من حالة تسجيل الدخول عند تحميل الصفحة
  checkLoginStatus();

  // تحديث عداد الزوار
  updateVisitorCount();
});

// وظيفة التحقق من حالة تسجيل الدخول
function checkLoginStatus() {
  const isLoggedIn = localStorage.getItem('loggedIn');
  const userData = localStorage.getItem('userData');
  const loginBtn = document.getElementById('loginBtn');
  const welcomeMessage = document.getElementById('welcomeMessage');

  if (isLoggedIn === 'true' && userData) {
    const user = JSON.parse(userData);
    loginBtn.innerHTML = `<span style="margin-right: 8px;">⏎</span>مرحباً ${user.fullName}`;
    loginBtn.style.backgroundColor = '#00cc7a';
    loginBtn.style.display = 'flex';
    loginBtn.style.alignItems = 'center';
    loginBtn.style.justifyContent = 'center';

    // إظهار رسالة الترحيب للمستخدمين المسجلين
    if (welcomeMessage) {
      welcomeMessage.style.display = 'block';
    }
  }
}

// وظيفة تحديث عداد الزوار
function updateVisitorCount() {
  const visitorCountElement = document.getElementById('visitorCount');
  if (visitorCountElement) {
    // محاكاة عداد الزوار
    let currentCount = localStorage.getItem('visitorCount');
    if (!currentCount) {
      currentCount = Math.floor(Math.random() * 1000) + 1200; // رقم عشوائي بين 1200-2200
      localStorage.setItem('visitorCount', currentCount);
    } else {
      currentCount = parseInt(currentCount) + 1;
      localStorage.setItem('visitorCount', currentCount);
    }

    // تحديث العرض مع تنسيق الأرقام
    visitorCountElement.textContent = currentCount.toLocaleString('ar-SA');
  }
}

// وظيفة التحقق من صحة النموذج
function validateForm() {
  const fullName = document.getElementById('fullName').value.trim();
  const email = document.getElementById('email').value.trim();
  const phone = document.getElementById('phone').value.trim();
  const password = document.getElementById('password').value;
  const confirmPassword = document.getElementById('confirmPassword').value;
  const birthdate = document.getElementById('birthdate').value;
  const country = document.getElementById('country').value;
  const experience = document.getElementById('experience').value;
  const ambitions = document.getElementById('ambitions').value.trim();
  const terms = document.getElementById('terms').checked;

  // التحقق من الحقول المطلوبة
  if (!fullName) {
    showError('يرجى إدخال الاسم الكامل');
    return false;
  }

  if (!email || !isValidEmail(email)) {
    showError('يرجى إدخال بريد إلكتروني صحيح');
    return false;
  }

  if (!phone || !isValidPhone(phone)) {
    showError('يرجى إدخال رقم هاتف صحيح');
    return false;
  }

  if (!password || password.length < 8) {
    showError('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
    return false;
  }

  if (password !== confirmPassword) {
    showError('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
    return false;
  }

  if (!birthdate) {
    showError('يرجى إدخال تاريخ الميلاد');
    return false;
  }

  if (!country) {
    showError('يرجى اختيار البلد');
    return false;
  }

  if (!experience) {
    showError('يرجى اختيار مستوى الخبرة');
    return false;
  }

  if (!ambitions || ambitions.length < 10) {
    showError('يرجى كتابة طموحاتك وأهدافك (10 أحرف على الأقل)');
    return false;
  }

  if (!terms) {
    showError('يجب الموافقة على الشروط والأحكام');
    return false;
  }

  return true;
}

// وظيفة التحقق من صحة البريد الإلكتروني
function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// وظيفة التحقق من صحة رقم الهاتف
function isValidPhone(phone) {
  const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
}

// وظيفة إظهار رسائل الخطأ
function showError(message) {
  alert('❌ ' + message);
}

// إضافة مستمعات للأزرار الاجتماعية
document.addEventListener('DOMContentLoaded', function() {
  // أزرار تسجيل الدخول الاجتماعي
  const githubBtn = document.querySelector('.github-btn');
  const googleBtn = document.querySelector('.google-btn');

  if (githubBtn) {
    githubBtn.addEventListener('click', function() {
      alert('🐙 تسجيل الدخول عبر GitHub قريباً!');
    });
  }

  if (googleBtn) {
    googleBtn.addEventListener('click', function() {
      alert('🔍 تسجيل الدخول عبر Google قريباً!');
    });
  }

  // إضافة تأثيرات بصرية للحقول
  const inputs = document.querySelectorAll('input, select, textarea');
  inputs.forEach(input => {
    input.addEventListener('focus', function() {
      this.style.transform = 'scale(1.02)';
      this.style.transition = 'transform 0.2s ease';
    });

    input.addEventListener('blur', function() {
      this.style.transform = 'scale(1)';
    });
  });
});
