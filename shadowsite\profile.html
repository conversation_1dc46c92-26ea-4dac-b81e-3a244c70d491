<!DOCTYPE html>
<html lang="ar">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ملف المطور - CyberSultan</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" integrity="sha512-oLJmpVEyZJklTz3i7KSpn9PTOzxyyHQ5SrjHkX9ez5sZ+YzDhIap35f58ICpbO3C0dk+14R0MpkG1kZfOz3AnQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />
  <style>
    body {
      background-color: #0d0d0d;
      color: #00ff99;
      font-family: 'Courier New', monospace;
      text-align: center;
      margin: 0;
      padding: 0;
      overflow-x: hidden;
    }
    
    @keyframes welcome-pulse {
      0%, 100% { color: #00ff99; }
      50% { color: #00ffe7; }
    }
    
    .welcome-message {
      font-size: 1.3em;
      color: #00ff99;
      margin-bottom: 15px;
      animation: welcome-pulse 3s infinite;
    }
    
    .profile-info {
      background: #121212;
      padding: 20px;
      border-radius: 10px;
      max-width: 600px;
      margin: 20px auto;
      text-align: right;
    }
    
    .profile-info p {
      margin: 10px 0;
      border-bottom: 1px solid #222;
      padding-bottom: 10px;
    }
    
    .profile-info strong {
      color: #00ffe7;
    }
    
    .buttons {
      margin: 25px 0;
    }
    
    .buttons a {
      display: inline-block;
      background: #121212;
      color: #00ff99;
      padding: 10px 15px;
      margin: 5px;
      border-radius: 5px;
      text-decoration: none;
      transition: all 0.3s ease;
    }
    
    .buttons a:hover {
      background: #1a1a1a;
      transform: translateY(-3px);
    }
    
    footer {
      margin-top: 40px;
      padding: 20px;
      background: #121212;
    }
  </style>
</head>
<body>

  <header>
    <h1><i class="fa-solid fa-user-secret"></i> المطور: CyberSultan101</h1>
  </header>

  <div class="welcome-message">
    مرحبًا بك يا <span id="username">عبدالله</span> في عالم <strong>CyberSultan</strong>!<br/>
    جهدك وتفانيك في مشروع <strong>ShadowSec Tools</strong> هو سبب تميزك،<br/>
    استمر في السعي لتحقيق أسمى الإنجازات في الأمن السيبراني! 🚀🔥
  </div>

  <img src="images/cybersultan.png" alt="صورة المطور" width="220" height="220" style="border-radius: 50%; border: 4px solid #00ff99; object-fit: cover; object-position: center top;" />

  <div class="profile-info">
    <p><strong>اسم المستخدم:</strong> <span id="profile-username"></span></p>
    <p><strong>البريد الإلكتروني:</strong> <span id="profile-email"></span></p>
    <p><strong>رقم الهاتف:</strong> <span id="profile-phone"></span></p>
    <p><strong>تاريخ الميلاد:</strong> <span id="profile-birthdate"></span></p>
    <p><strong>الطموح:</strong> <span id="profile-ambition"></span></p>
  </div>

  <div class="buttons">
    <a href="mailto:<EMAIL>"><i class="fa-solid fa-envelope"></i> تواصل عبر البريد</a>
    <a href="https://instagram.com/1sultan0_1" target="_blank" rel="noopener"><i class="fab fa-instagram"></i> Instagram</a>
    <a href="https://tiktok.com/@_sultan956" target="_blank" rel="noopener"><i class="fab fa-tiktok"></i> TikTok</a>
    <a href="https://github.com/fadlbda" target="_blank" rel="noopener"><i class="fab fa-github"></i> GitHub</a>
  </div>

  <div class="visitor-comment">
    <h3>تعليقات الزوار</h3>
    <div id="comments-container">
      <p>لا توجد تعليقات حتى الآن.</p>
    </div>
    <textarea id="new-comment" placeholder="أضف تعليقك هنا..." style="width: 80%; max-width: 500px; height: 80px; margin: 10px auto; display: block;"></textarea>
    <button id="submit-comment" style="background: #00ff99; color: #000; border: none; padding: 8px 15px; border-radius: 5px; cursor: pointer;">إرسال التعليق</button>
  </div>

  <footer>
    <p>جميع الحقوق محفوظة © 2025 - CyberSultan101</p>
  </footer>

  <script>
    // عرض بيانات المستخدم المسجل
    document.addEventListener('DOMContentLoaded', function() {
      // التحقق من تسجيل الدخول
      const isLoggedIn = localStorage.getItem('loggedIn');
      if (!isLoggedIn || isLoggedIn !== 'true') {
        window.location.href = 'login.html';
        return;
      }
      
      // عرض بيانات المستخدم
      const savedUsername = localStorage.getItem('username');
      const savedEmail = localStorage.getItem('email');
      const savedPhone = localStorage.getItem('phone');
      const savedBirthDate = localStorage.getItem('birthDate');
      const savedAmbition = localStorage.getItem('ambition');
      
      if (savedUsername) {
        document.getElementById('username').textContent = savedUsername;
        document.getElementById('profile-username').textContent = savedUsername;
      }
      
      if (savedEmail) {
        document.getElementById('profile-email').textContent = savedEmail;
      }
      
      if (savedPhone) {
        document.getElementById('profile-phone').textContent = savedPhone;
      }
      
      if (savedBirthDate) {
        document.getElementById('profile-birthdate').textContent = savedBirthDate;
      }
      
      if (savedAmbition) {
        document.getElementById('profile-ambition').textContent = savedAmbition;
      }
      
      // إضافة وظيفة التعليقات
      const commentBtn = document.getElementById('submit-comment');
      const commentsContainer = document.getElementById('comments-container');
      
      // تحميل التعليقات المحفوظة
      const savedComments = JSON.parse(localStorage.getItem('comments') || '[]');
      if (savedComments.length > 0) {
        commentsContainer.innerHTML = '';
        savedComments.forEach(comment => {
          const commentElement = document.createElement('div');
          commentElement.className = 'comment';
          commentElement.innerHTML = `
            <p><strong>${comment.username}:</strong> ${comment.text}</p>
            <small>${comment.date}</small>
          `;
          commentsContainer.appendChild(commentElement);
        });
      }
      
      commentBtn.addEventListener('click', function() {
        const commentText = document.getElementById('new-comment').value.trim();
        if (commentText) {
          // إضافة التعليق الجديد
          const newComment = {
            username: savedUsername || 'زائر',
            text: commentText,
            date: new Date().toLocaleDateString('ar-SA')
          };
          
          // حفظ التعليق
          const comments = JSON.parse(localStorage.getItem('comments') || '[]');
          comments.push(newComment);
          localStorage.setItem('comments', JSON.stringify(comments));
          
          // عرض التعليق
          const commentElement = document.createElement('div');
          commentElement.className = 'comment';
          commentElement.innerHTML = `
            <p><strong>${newComment.username}:</strong> ${newComment.text}</p>
            <small>${newComment.date}</small>
          `;
          
          if (commentsContainer.innerHTML === '<p>لا توجد تعليقات حتى الآن.</p>') {
            commentsContainer.innerHTML = '';
          }
          
          commentsContainer.appendChild(commentElement);
          document.getElementById('new-comment').value = '';
        }
      });
    });
  </script>
</body>
</html>
