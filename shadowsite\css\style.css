
body {
  font-family: 'Courier New', monospace;
  background-color: #0d0d0d;
  color: #00ff99;
  margin: 0;
  padding: 0;
}

header {
  background-color: #121212;
  text-align: center;
  padding: 25px 10px;
  border-bottom: 2px solid #00ff99;
}

header h1 {
  font-size: 2em;
  color: #00ffe7;
}

nav {
  margin-top: 10px;
}

nav a {
  color: #00ff99;
  margin: 0 15px;
  text-decoration: none;
}

#searchBox {
  margin-top: 20px;
  padding: 10px;
  width: 80%;
  max-width: 400px;
  border: none;
  border-radius: 5px;
  background-color: #1e1e1e;
  color: #00ff99;
  font-size: 16px;
}

/* إضافة نمط للأيقونة الافتراضية */
.tool-icon {
  width: 80px;
  height: 80px;
  background-color: #1e1e1e;
  color: #00ff99;
  font-size: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  margin: 0 auto 15px auto;
  border: 2px solid #00ff99;
}

.tool-card {
  background-color: #1a1a1a;
  margin: 20px auto;
  padding: 15px;
  max-width: 700px;
  border-radius: 10px;
  border: 1px solid #00ff99;
  box-shadow: 0 0 10px #00ff9933;
  transition: transform 0.2s ease;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.tool-card:hover {
  transform: scale(1.02);
  box-shadow: 0 0 20px #00ff99aa;
}

.tool-card::before {
  content: "🔒";
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 20px;
  opacity: 0.7;
}

.tool-card img {
  width: 80px;
  float: right;
  margin-left: 15px;
}

.tool-card h2 {
  margin-top: 0;
}

.tool-card a {
  color: #00ffe7;
  text-decoration: underline;
}

/* أنماط زر تسجيل الدخول */
.login-btn {
  background-color: #00ff99;
  color: #121212 !important;
  padding: 8px 15px;
  border-radius: 5px;
  font-weight: bold;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.login-btn:hover {
  background-color: #00cc7a;
  transform: scale(1.05);
}

/* تأثير خاص لأيقونة Enter */
.login-btn span {
  font-size: 16px;
  animation: enterGlow 2s infinite alternate;
}

@keyframes enterGlow {
  0% {
    opacity: 0.7;
    text-shadow: 0 0 5px rgba(18, 18, 18, 0.5);
  }
  100% {
    opacity: 1;
    text-shadow: 0 0 10px rgba(18, 18, 18, 0.8);
  }
}

/* أنماط رسالة الترحيب */
.welcome-message {
  background-color: rgba(0, 255, 153, 0.2);
  color: #00ff99;
  padding: 15px;
  border-radius: 10px;
  margin: 15px auto;
  border-left: 4px solid #00ff99;
  max-width: 600px;
}

.welcome-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.welcome-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 2px solid #00ff99;
  object-fit: cover;
  flex-shrink: 0;
}

.welcome-text h3 {
  margin: 0 0 5px 0;
  color: #00ffe7;
  font-size: 1.2em;
}

.welcome-text p {
  margin: 0;
  font-size: 0.9em;
  opacity: 0.9;
}

/* أنماط النافذة المنبثقة لتسجيل الدخول */
.modal {
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  -webkit-backdrop-filter: blur(5px);
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: #1a1a1a;
  margin: 5% auto;
  padding: 30px;
  border: 2px solid #00ff99;
  border-radius: 15px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 0 30px rgba(0, 255, 153, 0.3);
}

.close {
  color: #00ff99;
  float: right;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  transition: color 0.3s;
}

.close:hover {
  color: #00ffe7;
}

.modal-content h2 {
  color: #00ffe7;
  text-align: center;
  margin-bottom: 25px;
  font-size: 1.5em;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #00ff99;
  font-weight: bold;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #333;
  border-radius: 8px;
  background-color: #0d0d0d;
  color: #00ff99;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  transition: border-color 0.3s;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #00ff99;
  box-shadow: 0 0 10px rgba(0, 255, 153, 0.3);
}

.submit-btn {
  width: 100%;
  padding: 15px;
  background-color: #00ff99;
  color: #121212;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: 'Courier New', monospace;
}

.submit-btn:hover {
  background-color: #00cc7a;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 255, 153, 0.4);
}

/* تأثير خاص لزر الإرسال عند الضغط على Enter */
.submit-btn:focus {
  outline: none;
  box-shadow: 0 0 20px rgba(0, 255, 153, 0.6);
  transform: scale(1.02);
}

.submit-btn:active {
  transform: scale(0.98);
}

/* أنماط تلميح Enter في زر الإرسال */
.enter-hint {
  margin-left: 8px;
  font-size: 14px;
  opacity: 0.8;
  animation: enterPulse 1.5s infinite;
}

@keyframes enterPulse {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

/* تلميح النموذج */
.form-hint {
  text-align: center;
  font-size: 12px;
  color: rgba(0, 255, 153, 0.7);
  margin-top: 10px;
  font-style: italic;
}

/* أنماط عداد الزوار */
.visitor-counter {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: #1a1a1a;
  border: 2px solid #00ff99;
  border-radius: 50px;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 0 20px rgba(0, 255, 153, 0.3);
  z-index: 100;
  transition: all 0.3s ease;
}

.visitor-counter:hover {
  transform: scale(1.05);
  box-shadow: 0 0 30px rgba(0, 255, 153, 0.5);
}

.counter-icon {
  font-size: 20px;
  animation: pulse 2s infinite;
}

.counter-text {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  color: #00ff99;
}

.counter-text span:first-child {
  opacity: 0.8;
}

.counter-text span:last-child {
  font-weight: bold;
  font-size: 14px;
  color: #00ffe7;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;
  }

  .modal-content {
    margin: 10% auto;
    width: 95%;
    padding: 20px;
  }

  .visitor-counter {
    bottom: 10px;
    right: 10px;
    padding: 8px 15px;
  }

  .counter-text {
    font-size: 10px;
  }

  .counter-text span:last-child {
    font-size: 12px;
  }
}
