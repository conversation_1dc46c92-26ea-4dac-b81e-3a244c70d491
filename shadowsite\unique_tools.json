[{"name": "Nmap", "description": "شرح مختصر لأداة Nmap.", "image": "nmap.png", "download": "https://example.com/download/nmap", "command": "nmap --help"}, {"name": "SQLmap", "description": "شرح مختصر لأداة SQLmap.", "image": "sqlmap.png", "download": "https://example.com/download/sqlmap", "command": "sqlmap --help"}, {"name": "<PERSON><PERSON>", "description": "شرح مختصر لأداة Nikto.", "image": "nikto.png", "download": "https://example.com/download/nikto", "command": "nikto --help"}, {"name": "Hydra", "description": "شرح مختصر لأداة Hydra.", "image": "hydra.png", "download": "https://example.com/download/hydra", "command": "hydra --help"}, {"name": "Metasploit", "description": "شرح مختصر لأداة Metasploit.", "image": "metasploit.png", "download": "https://example.com/download/metasploit", "command": "metasploit --help"}, {"name": "Burp Suite", "description": "شرح مختصر لأداة Burp Suite.", "image": "burp_suite.png", "download": "https://example.com/download/burp-suite", "command": "burp --help"}, {"name": "<PERSON> Ripper", "description": "شرح مختصر لأداة John the Ripper.", "image": "john_the_ripper.png", "download": "https://example.com/download/john-the-ripper", "command": "john --help"}, {"name": "FFUF", "description": "شرح مختصر لأداة FFUF.", "image": "ffuf.png", "download": "https://example.com/download/ffuf", "command": "ffuf --help"}, {"name": "Gobuster", "description": "شرح مختصر لأداة Gobuster.", "image": "gobuster.png", "download": "https://example.com/download/gobuster", "command": "gobuster --help"}, {"name": "Dirb", "description": "شرح مختصر لأداة Dirb.", "image": "dirb.png", "download": "https://example.com/download/dirb", "command": "dirb --help"}, {"name": "Aircrack-ng", "description": "شرح مختصر لأداة Aircrack-ng.", "image": "aircrack-ng.png", "download": "https://example.com/download/aircrack-ng", "command": "aircrack-ng --help"}, {"name": "Wireshark", "description": "شرح مختصر لأداة Wireshark.", "image": "wireshark.png", "download": "https://example.com/download/wireshark", "command": "wireshark --help"}, {"name": "Tcpdump", "description": "شرح مختصر لأداة Tcpdump.", "image": "tcpdump.png", "download": "https://example.com/download/tcpdump", "command": "tcpdump --help"}, {"name": "<PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Ettercap.", "image": "ettercap.png", "download": "https://example.com/download/ettercap", "command": "ettercap --help"}, {"name": "<PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Kismet.", "image": "kismet.png", "download": "https://example.com/download/kismet", "command": "kismet --help"}, {"name": "Zmap", "description": "شرح مختصر لأداة Zmap.", "image": "zmap.png", "download": "https://example.com/download/zmap", "command": "zmap --help"}, {"name": "OpenVAS", "description": "شرح مختصر لأداة OpenVAS.", "image": "openvas.png", "download": "https://example.com/download/openvas", "command": "openvas --help"}, {"name": "Wapiti", "description": "شرح مختصر لأداة Wapiti.", "image": "wapiti.png", "download": "https://example.com/download/wapiti", "command": "wapiti --help"}, {"name": "Acunetix", "description": "شرح مختصر لأداة Acunetix.", "image": "acunetix.png", "download": "https://example.com/download/acunetix", "command": "acunetix --help"}, {"name": "Wfuzz", "description": "شرح مختصر لأداة Wfuzz.", "image": "wfuzz.png", "download": "https://example.com/download/wfuzz", "command": "wfuzz --help"}, {"name": "<PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة XSSer.", "image": "xsser.png", "download": "https://example.com/download/xsser", "command": "xsser --help"}, {"name": "BeEF", "description": "شرح مختصر لأداة BeEF.", "image": "beef.png", "download": "https://example.com/download/beef", "command": "beef --help"}, {"name": "Social-Engineer <PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Social-Engineer <PERSON><PERSON><PERSON>.", "image": "social-engineer_toolkit.png", "download": "https://example.com/download/social-engineer-toolkit", "command": "social-engineer --help"}, {"name": "TheHarvester", "description": "شرح مختصر لأداة TheHarvester.", "image": "theharvester.png", "download": "https://example.com/download/theharvester", "command": "theharvester --help"}, {"name": "Maltego", "description": "شرح مختصر لأداة Maltego.", "image": "maltego.png", "download": "https://example.com/download/maltego", "command": "maltego --help"}, {"name": "Recon-ng", "description": "شرح مختصر لأداة Recon-ng.", "image": "recon-ng.png", "download": "https://example.com/download/recon-ng", "command": "recon-ng --help"}, {"name": "<PERSON>", "description": "شرح مختصر لأداة Sherlock.", "image": "sherlock.png", "download": "https://example.com/download/sherlock", "command": "sherlock --help"}, {"name": "Shodan CLI", "description": "شرح مختصر لأداة Shodan CLI.", "image": "shodan_cli.png", "download": "https://example.com/download/shodan-cli", "command": "shodan --help"}, {"name": "Amass", "description": "شرح مختصر لأداة Amass.", "image": "amass.png", "download": "https://example.com/download/amass", "command": "amass --help"}, {"name": "Sublist3r", "description": "شرح مختصر لأداة Sublist3r.", "image": "sublist3r.png", "download": "https://example.com/download/sublist3r", "command": "sublist3r --help"}, {"name": "Dnsenum", "description": "شرح مختصر لأداة Dnsenum.", "image": "dnsenum.png", "download": "https://example.com/download/dnsenum", "command": "dnsenum --help"}, {"name": "Dnsrecon", "description": "شرح مختصر لأداة Dnsrecon.", "image": "dnsrecon.png", "download": "https://example.com/download/dnsrecon", "command": "dnsrecon --help"}, {"name": "WhatWeb", "description": "شرح مختصر لأداة WhatWeb.", "image": "whatweb.png", "download": "https://example.com/download/whatweb", "command": "whatweb --help"}, {"name": "Netcat", "description": "شرح مختصر لأداة Netcat.", "image": "netcat.png", "download": "https://example.com/download/netcat", "command": "netcat --help"}, {"name": "Hashcat", "description": "شرح مختصر لأداة Hashcat.", "image": "hashcat.png", "download": "https://example.com/download/hashcat", "command": "hashcat --help"}, {"name": "Crunch", "description": "شرح مختصر لأداة Crunch.", "image": "crunch.png", "download": "https://example.com/download/crunch", "command": "crunch --help"}, {"name": "Cewl", "description": "شرح مختصر لأداة Cewl.", "image": "cewl.png", "download": "https://example.com/download/cewl", "command": "cewl --help"}, {"name": "Hash-identifier", "description": "شرح مختصر لأداة Hash-identifier.", "image": "hash-identifier.png", "download": "https://example.com/download/hash-identifier", "command": "hash-identifier --help"}, {"name": "Binwalk", "description": "شرح مختصر لأداة Binwalk.", "image": "binwalk.png", "download": "https://example.com/download/binwalk", "command": "binwalk --help"}, {"name": "ExifTool", "description": "شرح مختصر لأداة ExifTool.", "image": "exiftool.png", "download": "https://example.com/download/exiftool", "command": "exiftool --help"}, {"name": "PeStudio", "description": "شرح مختصر لأداة PeStudio.", "image": "pestudio.png", "download": "https://example.com/download/pestudio", "command": "pestudio --help"}, {"name": "<PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Ghidra.", "image": "ghidra.png", "download": "https://example.com/download/ghidra", "command": "ghi<PERSON> --help"}, {"name": "Radare2", "description": "شرح مختصر لأداة Radare2.", "image": "radare2.png", "download": "https://example.com/download/radare2", "command": "radare2 --help"}, {"name": "OllyDbg", "description": "شرح مختصر لأداة OllyDbg.", "image": "ollydbg.png", "download": "https://example.com/download/ollydbg", "command": "ollydbg --help"}, {"name": "Immunity Debugger", "description": "شرح مختصر لأداة Immunity Debugger.", "image": "immunity_debugger.png", "download": "https://example.com/download/immunity-debugger", "command": "immunity --help"}, {"name": "APKTool", "description": "شرح مختصر لأداة APKTool.", "image": "apktool.png", "download": "https://example.com/download/apktool", "command": "apktool --help"}, {"name": "MobSF", "description": "شرح مختصر لأداة MobSF.", "image": "mobsf.png", "download": "https://example.com/download/mobsf", "command": "mobsf --help"}, {"name": "Frida", "description": "شرح مختصر لأداة Frida.", "image": "frida.png", "download": "https://example.com/download/frida", "command": "frida --help"}, {"name": "<PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Drozer.", "image": "drozer.png", "download": "https://example.com/download/drozer", "command": "drozer --help"}, {"name": "Qark", "description": "شرح مختصر لأداة Qark.", "image": "qark.png", "download": "https://example.com/download/qark", "command": "qark --help"}, {"name": "Volatility", "description": "شرح مختصر لأداة Volatility.", "image": "volatility.png", "download": "https://example.com/download/volatility", "command": "volatility --help"}, {"name": "Autopsy", "description": "شرح مختصر لأداة Autopsy.", "image": "autopsy.png", "download": "https://example.com/download/autopsy", "command": "autopsy --help"}, {"name": "FTK Imager", "description": "شرح مختصر لأداة FTK Imager.", "image": "ftk_imager.png", "download": "https://example.com/download/ftk-imager", "command": "ftk --help"}, {"name": "Sleuth Kit", "description": "شرح مختصر لأداة Sleuth Kit.", "image": "sleuth_kit.png", "download": "https://example.com/download/sleuth-kit", "command": "sleuth --help"}, {"name": "YARA", "description": "شرح مختصر لأداة YARA.", "image": "yara.png", "download": "https://example.com/download/yara", "command": "yara --help"}, {"name": "Chkrootkit", "description": "شرح مختصر لأداة Chkrootkit.", "image": "chkrootkit.png", "download": "https://example.com/download/chkrootkit", "command": "chkrootkit --help"}, {"name": "<PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Lynis.", "image": "lynis.png", "download": "https://example.com/download/lynis", "command": "lynis --help"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Rkhunter.", "image": "rkhunter.png", "download": "https://example.com/download/rkhunter", "command": "rkhunter --help"}, {"name": "ClamAV", "description": "شرح مختصر لأداة ClamAV.", "image": "clamav.png", "download": "https://example.com/download/clamav", "command": "clamav --help"}, {"name": "<PERSON><PERSON>", "description": "شرح مختصر لأداة Nikto.", "image": "nikto.png", "download": "https://example.com/download/nikto", "command": "nikto --help"}, {"name": "SSLScan", "description": "شرح مختصر لأداة SSLScan.", "image": "sslscan.png", "download": "https://example.com/download/sslscan", "command": "sslscan --help"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة SSLLabs Scanner.", "image": "ssllabs_scanner.png", "download": "https://example.com/download/ssllabs-scanner", "command": "ssllabs --help"}, {"name": "TestSSL.sh", "description": "شرح مختصر لأداة TestSSL.sh.", "image": "testssl.sh.png", "download": "https://example.com/download/testssl.sh", "command": "testssl.sh --help"}, {"name": "Wpscan", "description": "شرح مختصر لأداة Wpscan.", "image": "wpscan.png", "download": "https://example.com/download/wpscan", "command": "wpscan --help"}, {"name": "WPScan-CLI", "description": "شرح مختصر لأداة WPScan-CLI.", "image": "wpscan-cli.png", "download": "https://example.com/download/wpscan-cli", "command": "wpscan-cli --help"}, {"name": "Joomscan", "description": "شرح مختصر لأداة Joomscan.", "image": "joomscan.png", "download": "https://example.com/download/joomscan", "command": "joomscan --help"}, {"name": "CMSmap", "description": "شرح مختصر لأداة CMSmap.", "image": "cmsmap.png", "download": "https://example.com/download/cmsmap", "command": "cmsmap --help"}, {"name": "Skipfish", "description": "شرح مختصر لأداة Skipfish.", "image": "skipfish.png", "download": "https://example.com/download/skipfish", "command": "skipfish --help"}, {"name": "OWASP ZAP", "description": "شرح مختصر لأداة OWASP ZAP.", "image": "owasp_zap.png", "download": "https://example.com/download/owasp-zap", "command": "owasp --help"}, {"name": "IronWASP", "description": "شرح مختصر لأداة IronWASP.", "image": "ironwasp.png", "download": "https://example.com/download/ironwasp", "command": "ironwasp --help"}, {"name": "Sqlninja", "description": "شرح مختصر لأداة Sqlninja.", "image": "sqlninja.png", "download": "https://example.com/download/sqlninja", "command": "sqlninja --help"}, {"name": "NoSQLMap", "description": "شرح مختصر لأداة NoSQLMap.", "image": "nosqlmap.png", "download": "https://example.com/download/nosqlmap", "command": "nosqlmap --help"}, {"name": "Mongoaudit", "description": "شرح مختصر لأداة Mongoaudit.", "image": "mongoaudit.png", "download": "https://example.com/download/mongoaudit", "command": "mongoaudit --help"}, {"name": "JWT Tool", "description": "شرح مختصر لأداة JWT Tool.", "image": "jwt_tool.png", "download": "https://example.com/download/jwt-tool", "command": "jwt --help"}, {"name": "JWT Cracker", "description": "شرح مختصر لأداة JWT Cracker.", "image": "jwt_cracker.png", "download": "https://example.com/download/jwt-cracker", "command": "jwt --help"}, {"name": "Burp Collaborator", "description": "شرح مختصر لأداة Burp Collaborator.", "image": "burp_collaborator.png", "download": "https://example.com/download/burp-collaborator", "command": "burp --help"}, {"name": "XSS Hunter", "description": "شرح مختصر لأداة XSS Hunter.", "image": "xss_hunter.png", "download": "https://example.com/download/xss-hunter", "command": "xss --help"}, {"name": "Corsy", "description": "شرح مختصر لأداة Corsy.", "image": "corsy.png", "download": "https://example.com/download/corsy", "command": "corsy --help"}, {"name": "XSRFProbe", "description": "شرح مختصر لأداة XSRFProbe.", "image": "xsrfprobe.png", "download": "https://example.com/download/xsrfprobe", "command": "xsrfprobe --help"}, {"name": "XSStrike", "description": "شرح مختصر لأداة XSStrike.", "image": "xsstrike.png", "download": "https://example.com/download/xsstrike", "command": "xsstrike --help"}, {"name": "Arpwatch", "description": "شرح مختصر لأداة Arpwatch.", "image": "arpwatch.png", "download": "https://example.com/download/arpwatch", "command": "arpwatch --help"}, {"name": "Netdiscover", "description": "شرح مختصر لأداة Netdiscover.", "image": "netdiscover.png", "download": "https://example.com/download/netdiscover", "command": "netdiscover --help"}, {"name": "Bettercap", "description": "شرح مختصر لأداة Bettercap.", "image": "bettercap.png", "download": "https://example.com/download/bettercap", "command": "bettercap --help"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Responder.", "image": "responder.png", "download": "https://example.com/download/responder", "command": "responder --help"}, {"name": "MITMf", "description": "شرح مختصر لأداة MITMf.", "image": "mitmf.png", "download": "https://example.com/download/mitmf", "command": "mitmf --help"}, {"name": "FruityWiFi", "description": "شرح مختصر لأداة FruityWiFi.", "image": "fruitywifi.png", "download": "https://example.com/download/fruitywifi", "command": "fruitywifi --help"}, {"name": "EvilAP", "description": "شرح مختصر لأداة EvilAP.", "image": "evilap.png", "download": "https://example.com/download/evilap", "command": "evilap --help"}, {"name": "Wifite", "description": "شرح مختصر لأداة Wifite.", "image": "wifite.png", "download": "https://example.com/download/wifite", "command": "wifite --help"}, {"name": "Airgeddon", "description": "شرح مختصر لأداة Airgeddon.", "image": "airgeddon.png", "download": "https://example.com/download/airgeddon", "command": "airgeddon --help"}, {"name": "PixieWPS", "description": "شرح مختصر لأداة PixieWPS.", "image": "pixiewps.png", "download": "https://example.com/download/pixiewps", "command": "pixiewps --help"}, {"name": "<PERSON><PERSON>", "description": "شرح مختصر لأداة Reaver.", "image": "reaver.png", "download": "https://example.com/download/reaver", "command": "reaver --help"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Cowpatty.", "image": "cowpatty.png", "download": "https://example.com/download/cowpatty", "command": "cowpatty --help"}, {"name": "<PERSON><PERSON>", "description": "شرح مختصر لأداة Fern WiFi Cracker.", "image": "fern_wifi_cracker.png", "download": "https://example.com/download/fern-wifi-cracker", "command": "fern --help"}, {"name": "<PERSON>", "description": "شرح مختصر لأداة Ghost Phisher.", "image": "ghost_phisher.png", "download": "https://example.com/download/ghost-phisher", "command": "ghost --help"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Wifiphisher.", "image": "wifiphisher.png", "download": "https://example.com/download/wifiphisher", "command": "wifiphisher --help"}, {"name": "Ettercap GUI", "description": "شرح مختصر لأداة Ettercap GUI.", "image": "ettercap_gui.png", "download": "https://example.com/download/ettercap-gui", "command": "ettercap --help"}, {"name": "Yersinia", "description": "شرح مختصر لأداة Yersinia.", "image": "yersinia.png", "download": "https://example.com/download/yersinia", "command": "yer<PERSON>ia --help"}, {"name": "Scapy", "description": "شرح مختصر لأداة Scapy.", "image": "scapy.png", "download": "https://example.com/download/scapy", "command": "scapy --help"}, {"name": "Hping3", "description": "شرح مختصر لأداة Hping3.", "image": "hping3.png", "download": "https://example.com/download/hping3", "command": "hping3 --help"}, {"name": "Ncrack", "description": "شرح مختصر لأداة Ncrack.", "image": "ncrack.png", "download": "https://example.com/download/ncrack", "command": "ncrack --help"}, {"name": "THC-SSL-DOS", "description": "شرح مختصر لأداة THC-SSL-DOS.", "image": "thc-ssl-dos.png", "download": "https://example.com/download/thc-ssl-dos", "command": "thc-ssl-dos --help"}, {"name": "<PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Slowloris.", "image": "slowloris.png", "download": "https://example.com/download/slowloris", "command": "slowloris --help"}, {"name": "GoldenEye", "description": "شرح مختصر لأداة GoldenEye.", "image": "goldeneye.png", "download": "https://example.com/download/goldeneye", "command": "goldeneye --help"}, {"name": "LOIC", "description": "شرح مختصر لأداة LOIC.", "image": "loic.png", "download": "https://example.com/download/loic", "command": "loic --help"}, {"name": "HOIC", "description": "شرح مختصر لأداة HOIC.", "image": "hoic.png", "download": "https://example.com/download/hoic", "command": "hoic --help"}, {"name": "Tor", "description": "شرح مختصر لأداة Tor.", "image": "tor.png", "download": "https://example.com/download/tor", "command": "tor --help"}, {"name": "ProxyChains", "description": "شرح مختصر لأداة ProxyChains.", "image": "proxychains.png", "download": "https://example.com/download/proxychains", "command": "proxychains --help"}, {"name": "An<PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Anonsurf.", "image": "anonsurf.png", "download": "https://example.com/download/anonsurf", "command": "anonsurf --help"}, {"name": "I2P", "description": "شرح مختصر لأداة I2P.", "image": "i2p.png", "download": "https://example.com/download/i2p", "command": "i2p --help"}, {"name": "Tailscale", "description": "شرح مختصر لأداة Tailscale.", "image": "tailscale.png", "download": "https://example.com/download/tailscale", "command": "tailscale --help"}, {"name": "Setoolkit", "description": "شرح مختصر لأداة Setoolkit.", "image": "setoolkit.png", "download": "https://example.com/download/setoolkit", "command": "setoolkit --help"}, {"name": "<PERSON><PERSON>", "description": "شرح مختصر لأداة Veil.", "image": "veil.png", "download": "https://example.com/download/veil", "command": "veil --help"}, {"name": "TheFatRat", "description": "شرح مختصر لأداة TheFatRat.", "image": "thefatrat.png", "download": "https://example.com/download/thefatrat", "command": "thefatrat --help"}, {"name": "Empire", "description": "شرح مختصر لأداة Empire.", "image": "empire.png", "download": "https://example.com/download/empire", "command": "empire --help"}, {"name": "Covenant", "description": "شرح مختصر لأداة Covenant.", "image": "covenant.png", "download": "https://example.com/download/covenant", "command": "covenant --help"}, {"name": "Koadic", "description": "شرح مختصر لأداة Koadic.", "image": "koadic.png", "download": "https://example.com/download/koadic", "command": "koa<PERSON> --help"}, {"name": "<PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Nishang.", "image": "nishang.png", "download": "https://example.com/download/nishang", "command": "nishang --help"}, {"name": "PowerSploit", "description": "شرح مختصر لأداة PowerSploit.", "image": "powersploit.png", "download": "https://example.com/download/powersploit", "command": "powersploit --help"}, {"name": "CrackMapExec", "description": "شرح مختصر لأداة CrackMapExec.", "image": "crackmapexec.png", "download": "https://example.com/download/crackmapexec", "command": "crackmapexec --help"}, {"name": "BloodHound", "description": "شرح مختصر لأداة BloodHound.", "image": "bloodhound.png", "download": "https://example.com/download/bloodhound", "command": "bloodhound --help"}, {"name": "SharpHound", "description": "شرح مختصر لأداة SharpHound.", "image": "sharphound.png", "download": "https://example.com/download/sharphound", "command": "sharphound --help"}, {"name": "Impacket", "description": "شرح مختصر لأداة Impacket.", "image": "impacket.png", "download": "https://example.com/download/impacket", "command": "impacket --help"}, {"name": "Mimikatz", "description": "شرح مختصر لأداة Mimikatz.", "image": "mimikatz.png", "download": "https://example.com/download/mimikatz", "command": "mimikatz --help"}, {"name": "PsExec", "description": "شرح مختصر لأداة PsExec.", "image": "psexec.png", "download": "https://example.com/download/psexec", "command": "psexec --help"}, {"name": "WinPEAS", "description": "شرح مختصر لأداة WinPEAS.", "image": "winpeas.png", "download": "https://example.com/download/winpeas", "command": "winpeas --help"}, {"name": "LinPEAS", "description": "شرح مختصر لأداة LinPEAS.", "image": "linpeas.png", "download": "https://example.com/download/linpeas", "command": "linpeas --help"}, {"name": "LES", "description": "شرح مختصر لأداة LES.", "image": "les.png", "download": "https://example.com/download/les", "command": "les --help"}, {"name": "GTFOBins", "description": "شرح مختصر لأداة GTFOBins.", "image": "gtfobins.png", "download": "https://example.com/download/gtfobins", "command": "gtfobins --help"}, {"name": "Linux Exploit Suggester", "description": "شرح مختصر لأداة Linux Exploit Suggester.", "image": "linux_exploit_suggester.png", "download": "https://example.com/download/linux-exploit-suggester", "command": "linux --help"}, {"name": "Windows Exploit Suggester", "description": "شرح مختصر لأداة Windows Exploit Suggester.", "image": "windows_exploit_suggester.png", "download": "https://example.com/download/windows-exploit-suggester", "command": "windows --help"}, {"name": "SecLists", "description": "شرح مختصر لأداة SecLists.", "image": "seclists.png", "download": "https://example.com/download/seclists", "command": "seclists --help"}, {"name": "PayloadsAllTheThings", "description": "شرح مختصر لأداة PayloadsAllTheThings.", "image": "payloadsallthethings.png", "download": "https://example.com/download/payloadsallthethings", "command": "payloadsallthethings --help"}, {"name": "FfufPlus", "description": "شرح مختصر لأداة FfufPlus.", "image": "ffufplus.png", "download": "https://example.com/download/ffufplus", "command": "ffufplus --help"}, {"name": "WAFW00F", "description": "شرح مختصر لأداة WAFW00F.", "image": "wafw00f.png", "download": "https://example.com/download/wafw00f", "command": "wafw00f --help"}, {"name": "<PERSON>nort", "description": "شرح مختصر لأداة Snort.", "image": "snort.png", "download": "https://example.com/download/snort", "command": "snort --help"}, {"name": "Suricata", "description": "شرح مختصر لأداة Suricata.", "image": "suricata.png", "download": "https://example.com/download/suricata", "command": "suricata --help"}, {"name": "Bro/Zeek", "description": "شرح مختصر لأداة Bro/Zeek.", "image": "bro_zeek.png", "download": "https://example.com/download/bro-zeek", "command": "bro/zeek --help"}, {"name": "OSSEC", "description": "شرح مختصر لأداة OSSEC.", "image": "ossec.png", "download": "https://example.com/download/ossec", "command": "ossec --help"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Splunk.", "image": "splunk.png", "download": "https://example.com/download/splunk", "command": "splunk --help"}, {"name": "Graylog", "description": "شرح مختصر لأداة Graylog.", "image": "graylog.png", "download": "https://example.com/download/graylog", "command": "graylog --help"}, {"name": "ELK Stack", "description": "شرح مختصر لأداة ELK Stack.", "image": "elk_stack.png", "download": "https://example.com/download/elk-stack", "command": "elk --help"}, {"name": "<PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Sysmon.", "image": "sysmon.png", "download": "https://example.com/download/sysmon", "command": "sysmon --help"}, {"name": "SysInternals", "description": "شرح مختصر لأداة SysInternals.", "image": "sysinternals.png", "download": "https://example.com/download/sysinternals", "command": "sysinternals --help"}, {"name": "<PERSON><PERSON><PERSON>", "description": "شرح مختصر لأداة Procmon.", "image": "procmon.png", "download": "https://example.com/download/procmon", "command": "procmon --help"}, {"name": "Autoruns", "description": "شرح مختصر لأداة Autoruns.", "image": "autoruns.png", "download": "https://example.com/download/autoruns", "command": "autoruns --help"}, {"name": "Sysdig", "description": "شرح مختصر لأداة Sysdig.", "image": "sysdig.png", "download": "https://example.com/download/sysdig", "command": "sysdig --help"}, {"name": "Falco", "description": "شرح مختصر لأداة Falco.", "image": "falco.png", "download": "https://example.com/download/falco", "command": "falco --help"}, {"name": "Auditd", "description": "شرح مختصر لأداة Auditd.", "image": "auditd.png", "download": "https://example.com/download/auditd", "command": "auditd --help"}, {"name": "Logwatch", "description": "شرح مختصر لأداة Logwatch.", "image": "logwatch.png", "download": "https://example.com/download/logwatch", "command": "logwatch --help"}, {"name": "Logrotate", "description": "شرح مختصر لأداة Logrotate.", "image": "logrotate.png", "download": "https://example.com/download/logrotate", "command": "logrotate --help"}]